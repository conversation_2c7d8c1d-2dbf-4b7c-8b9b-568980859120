// API Response Types
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  success: boolean;
  errors?: string[];
}

// Authentication Types
export interface LoginModel {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface AuthResponse {
  token: string;
  refreshToken: string;
  user: UserDto;
  expiresAt: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

// Education Management System Types (based on actual API)
export type UserStatus = number; // 1 = Student, 2 = Admin
export type Gender = 'Male' | 'Female' | 'Other';

export interface UserDto {
  // System fields (from actual API)
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  status?: UserStatus;
  isDeleted?: boolean;

  // Required user info (from actual API)
  firstName: string;
  lastName: string;

  // Personal details (from actual API)
  gender?: Gender;
  idNumber?: string;
  idType?: string;
  dateOfBirth?: Date;
  role?: string;
  email?: string;
  userName?: string;
  fullName?: string; // Read-only computed field
}

export interface CreateUserRequest extends Omit<UserDto, 'id' | 'createdBy' | 'dateCreated' | 'fullName'> {}
export interface UpdateUserRequest extends Partial<CreateUserRequest> {}

// API Client Types
export interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
}

export interface RequestConfig {
  headers?: Record<string, string>;
  params?: Record<string, any>;
}

// Export table interfaces
export * from './table';

// Grading System Types
export type GradeLevel = 'A' | 'B' | 'C' | 'D' | 'F';
export type ScoreType = 'first' | 'verify' | 'remark' | 'final' | 'regression' | 'absent';

export interface GradeDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Grade details
  studentId: string;
  subjectId: string;
  paperId: string;
  year: number;
  startScore: number;
  endScore: number;
  assignedGrade: GradeLevel;
  failResult: boolean;
}

export interface SubjectDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Subject details
  name: string;
  code: string;
  description?: string;
  isActive: boolean;
}

export interface PaperDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Paper details
  subjectId: string;
  name: string;
  code: string;
  description?: string;
  paperNumber: number;
  duration?: number; // Duration in minutes
  maxMarks?: number;
  isActive: boolean;
}

export interface GradeBoundaryDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Grade boundary details
  subjectId: string;
  paperId: string;
  year: number;
  gradeLevel: GradeLevel;
  minScore: number;
  maxScore: number;
  description?: string;
  isActive: boolean;
}

export interface GradeConfigurationDto {
  id?: string;
  createdBy?: string;
  dateCreated?: Date;
  isDeleted?: boolean;

  // Configuration details
  year: number;
  isActive: boolean;
  description?: string;
  gradeBoundaries: GradeBoundaryDto[];
}

// Request types for grading system
export interface CreateGradeRequest extends Omit<GradeDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeRequest extends Partial<CreateGradeRequest> {}

export interface CreateSubjectRequest extends Omit<SubjectDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateSubjectRequest extends Partial<CreateSubjectRequest> {}

export interface CreatePaperRequest extends Omit<PaperDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdatePaperRequest extends Partial<CreatePaperRequest> {}

export interface CreateGradeBoundaryRequest extends Omit<GradeBoundaryDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeBoundaryRequest extends Partial<CreateGradeBoundaryRequest> {}

export interface CreateGradeConfigurationRequest extends Omit<GradeConfigurationDto, 'id' | 'createdBy' | 'dateCreated'> {}
export interface UpdateGradeConfigurationRequest extends Partial<CreateGradeConfigurationRequest> {}

// Results Module Interfaces
export interface StudentResultDto {
  id?: string;
  studentId: string;
  subjectId: string;
  paperId: string;
  year: number;
  score: number;
  calculatedGrade: GradeLevel;
  passStatus: boolean;
  dateCalculated?: Date;
}

export interface ResultsFilterDto {
  searchQuery?: string;
  year?: number | 'All';
  subjectId?: string | 'All';
  paperId?: string | 'All';
  gradeLevel?: GradeLevel | 'All';
  passStatus?: boolean | 'All';
}