<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ isEditing ? 'Edit Grade' : 'Add New Grade' }}
              </h3>

              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Student ID -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Student ID</label>
                  <input
                    v-model="formData.studentId"
                    type="text"
                    required
                    placeholder="Enter student ID"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Subject -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select
                    v-model="formData.subjectId"
                    required
                    @change="onSubjectChange"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  >
                    <option value="">Select a subject</option>
                    <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                      {{ subject.name }} ({{ subject.code }})
                    </option>
                  </select>
                </div>

                <!-- Paper -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Paper</label>
                  <select
                    v-model="formData.paperId"
                    required
                    :disabled="!formData.subjectId"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm disabled:bg-gray-100"
                  >
                    <option value="">Select a paper</option>
                    <option v-for="paper in availablePapers" :key="paper.id" :value="paper.id">
                      {{ paper.name }}
                    </option>
                  </select>
                </div>

                <!-- Year -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
                  <input
                    v-model.number="formData.year"
                    type="number"
                    required
                    min="2020"
                    :max="new Date().getFullYear() + 1"
                    placeholder="Enter year"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Score Range -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Start Score</label>
                    <input
                      v-model.number="formData.startScore"
                      type="number"
                      required
                      min="0"
                      max="100"
                      placeholder="Start score"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">End Score</label>
                    <input
                      v-model.number="formData.endScore"
                      type="number"
                      required
                      min="0"
                      max="100"
                      placeholder="End score"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                </div>

                <!-- Assigned Grade -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Assigned Grade</label>
                  <select
                    v-model="formData.assignedGrade"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  >
                    <option value="">Select a grade</option>
                    <option value="A">A (Distinction)</option>
                    <option value="B">B (Credit)</option>
                    <option value="C">C (Pass)</option>
                    <option value="D">D (Pass)</option>
                    <option value="F">F (Fail)</option>
                  </select>
                </div>

                <!-- Fail Result -->
                <div class="flex items-center">
                  <input
                    v-model="formData.failResult"
                    type="checkbox"
                    id="failResult"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                  />
                  <label for="failResult" class="ml-2 block text-sm text-gray-700">
                    Mark as fail result
                  </label>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- Grade Preview -->
                <div v-if="formData.assignedGrade" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Grade Preview</h4>
                  <div class="flex items-center justify-between">
                    <div>
                      <span class="text-sm text-gray-600">Grade: </span>
                      <span :class="getGradeBadgeClass(formData.assignedGrade)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                        {{ formData.assignedGrade }}
                      </span>
                    </div>
                    <div>
                      <span class="text-sm text-gray-600">Score Range: </span>
                      <span class="text-sm font-medium text-gray-900">{{ formData.startScore }} - {{ formData.endScore }}</span>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="isLoading || !isFormValid"
            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Saving...' : (isEditing ? 'Update Grade' : 'Add Grade') }}
          </button>
          <button
            @click="closeModal"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import type { GradeDto, GradeLevel, CreateGradeRequest, UpdateGradeRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  grade?: GradeDto | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  grade: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const formData = ref<{
  studentId: string
  subjectId: string
  paperId: string
  year: number
  startScore: number
  endScore: number
  assignedGrade: GradeLevel | ''
  failResult: boolean
}>({
  studentId: '',
  subjectId: '',
  paperId: '',
  year: new Date().getFullYear(),
  startScore: 0,
  endScore: 0,
  assignedGrade: '',
  failResult: false
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isEditing = computed(() => !!props.grade?.id)

const isFormValid = computed(() => {
  return formData.value.studentId &&
         formData.value.subjectId &&
         formData.value.paperId &&
         formData.value.year &&
         formData.value.startScore >= 0 &&
         formData.value.endScore >= 0 &&
         formData.value.startScore <= formData.value.endScore &&
         formData.value.assignedGrade
})

const availablePapers = computed(() => {
  if (!formData.value.subjectId) return []
  return gradingStore.papers.filter(paper =>
    paper.subjectId === formData.value.subjectId && paper.isActive && !paper.isDeleted
  )
})

// Methods
const getGradeBadgeClass = (grade: string): string => {
  const classes = {
    'A': 'bg-green-100 text-green-800',
    'B': 'bg-blue-100 text-blue-800',
    'C': 'bg-yellow-100 text-yellow-800',
    'D': 'bg-orange-100 text-orange-800',
    'F': 'bg-red-100 text-red-800'
  }
  return classes[grade as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const resetForm = () => {
  formData.value = {
    studentId: '',
    subjectId: '',
    paperId: '',
    year: new Date().getFullYear(),
    startScore: 0,
    endScore: 0,
    assignedGrade: '',
    failResult: false
  }
  error.value = null
}

const onSubjectChange = () => {
  // Reset paper selection when subject changes
  formData.value.paperId = ''
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    if (isEditing.value && props.grade?.id) {
      // Update existing grade
      const updateData: UpdateGradeRequest = {
        studentId: formData.value.studentId,
        subjectId: formData.value.subjectId,
        paperId: formData.value.paperId,
        year: formData.value.year,
        startScore: formData.value.startScore,
        endScore: formData.value.endScore,
        assignedGrade: formData.value.assignedGrade as GradeLevel,
        failResult: formData.value.failResult
      }
      await gradingStore.updateGrade(props.grade.id, updateData)
      await sweetAlert.toast.success('Grade updated successfully')
    } else {
      // Create new grade
      const createData: CreateGradeRequest = {
        studentId: formData.value.studentId,
        subjectId: formData.value.subjectId,
        paperId: formData.value.paperId,
        year: formData.value.year,
        startScore: formData.value.startScore,
        endScore: formData.value.endScore,
        assignedGrade: formData.value.assignedGrade as GradeLevel,
        failResult: formData.value.failResult
      }
      await gradingStore.createGrade(createData)
      await sweetAlert.toast.success('Grade added successfully')
    }

    emit('success')
  } catch (err: any) {
    error.value = err.message || 'Failed to save grade'
  } finally {
    isLoading.value = false
  }
}

// Watch for grade prop changes
watch(() => props.grade, (newGrade) => {
  if (newGrade) {
    formData.value = {
      studentId: newGrade.studentId || '',
      subjectId: newGrade.subjectId || '',
      paperId: newGrade.paperId || '',
      year: newGrade.year || new Date().getFullYear(),
      startScore: newGrade.startScore || 0,
      endScore: newGrade.endScore || 0,
      assignedGrade: newGrade.assignedGrade || '',
      failResult: newGrade.failResult || false
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
