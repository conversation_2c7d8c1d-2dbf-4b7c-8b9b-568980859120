import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { resultsService } from '@/services/results.service';
import type { 
  StudentResultDto, 
  ResultsFilterDto,
  GradeLevel,
  GradeBoundaryDto,
  SubjectDto,
  PaperDto
} from '@/interfaces';

export const useResultsStore = defineStore('results', () => {
  // State
  const results = ref<StudentResultDto[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  
  // Filters
  const filters = ref<ResultsFilterDto>({
    searchQuery: '',
    year: 'All',
    subjectId: 'All',
    paperId: 'All',
    gradeLevel: 'All',
    passStatus: 'All'
  });

  // Statistics
  const statistics = ref({
    totalResults: 0,
    passCount: 0,
    failCount: 0,
    gradeDistribution: {
      'A': 0,
      'B': 0,
      'C': 0,
      'D': 0,
      'F': 0
    } as { [key in GradeLevel]: number },
    passRate: 0
  });

  // Computed
  const filteredResults = computed(() => {
    let filtered = results.value;

    // Apply search filter
    if (filters.value.searchQuery) {
      const query = filters.value.searchQuery.toLowerCase();
      filtered = filtered.filter(result => 
        result.studentId.toLowerCase().includes(query) ||
        result.calculatedGrade.toLowerCase().includes(query)
      );
    }

    // Apply year filter
    if (filters.value.year && filters.value.year !== 'All') {
      filtered = filtered.filter(result => result.year === filters.value.year);
    }

    // Apply subject filter
    if (filters.value.subjectId && filters.value.subjectId !== 'All') {
      filtered = filtered.filter(result => result.subjectId === filters.value.subjectId);
    }

    // Apply paper filter
    if (filters.value.paperId && filters.value.paperId !== 'All') {
      filtered = filtered.filter(result => result.paperId === filters.value.paperId);
    }

    // Apply grade filter
    if (filters.value.gradeLevel && filters.value.gradeLevel !== 'All') {
      filtered = filtered.filter(result => result.calculatedGrade === filters.value.gradeLevel);
    }

    // Apply pass status filter
    if (filters.value.passStatus !== undefined && filters.value.passStatus !== 'All') {
      filtered = filtered.filter(result => result.passStatus === filters.value.passStatus);
    }

    return filtered.sort((a, b) => {
      // Sort by year (desc), then by student ID, then by subject
      if (a.year !== b.year) return b.year - a.year;
      if (a.studentId !== b.studentId) return a.studentId.localeCompare(b.studentId);
      return a.subjectId.localeCompare(b.subjectId);
    });
  });

  const availableYears = computed(() => {
    const years = [...new Set(results.value.map(r => r.year))].sort((a, b) => b - a);
    return years;
  });

  const availablePapers = computed(() => {
    if (filters.value.subjectId && filters.value.subjectId !== 'All') {
      // Return papers for selected subject only
      return [...new Set(results.value
        .filter(r => r.subjectId === filters.value.subjectId)
        .map(r => r.paperId))];
    }
    // Return all papers
    return [...new Set(results.value.map(r => r.paperId))];
  });

  // Actions
  const clearError = () => {
    error.value = null;
  };

  const resetFilters = () => {
    filters.value = {
      searchQuery: '',
      year: 'All',
      subjectId: 'All',
      paperId: 'All',
      gradeLevel: 'All',
      passStatus: 'All'
    };
  };

  const generateResults = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    isLoading.value = true;
    error.value = null;
    try {
      results.value = await resultsService.generateResults(gradeBoundaries, subjects, papers);
      await updateStatistics(gradeBoundaries, subjects, papers);
    } catch (err: any) {
      error.value = err.message || 'Failed to generate results';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateStatistics = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    try {
      statistics.value = await resultsService.getResultStatistics(gradeBoundaries, subjects, papers);
    } catch (err: any) {
      console.error('Failed to update statistics:', err);
    }
  };

  const generateCertificate = async (studentId: string, year: number) => {
    isLoading.value = true;
    error.value = null;
    try {
      const certificate = await resultsService.generateCertificate(studentId, year);
      return certificate;
    } catch (err: any) {
      error.value = err.message || 'Failed to generate certificate';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const refreshResults = async (
    gradeBoundaries: GradeBoundaryDto[],
    subjects: SubjectDto[],
    papers: PaperDto[]
  ) => {
    await generateResults(gradeBoundaries, subjects, papers);
  };

  // Helper methods for getting names
  const getSubjectName = (subjectId: string, subjects: SubjectDto[]): string => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject?.name || 'Unknown Subject';
  };

  const getPaperName = (paperId: string, papers: PaperDto[]): string => {
    const paper = papers.find(p => p.id === paperId);
    return paper?.name || 'Unknown Paper';
  };

  const getGradeBadgeClass = (grade: GradeLevel): string => {
    const classes = {
      'A': 'bg-green-100 text-green-800',
      'B': 'bg-blue-100 text-blue-800',
      'C': 'bg-yellow-100 text-yellow-800',
      'D': 'bg-orange-100 text-orange-800',
      'F': 'bg-red-100 text-red-800'
    };
    return classes[grade] || 'bg-gray-100 text-gray-800';
  };

  return {
    // State
    results,
    isLoading,
    error,
    filters,
    statistics,
    
    // Getters
    filteredResults,
    availableYears,
    availablePapers,
    
    // Actions
    clearError,
    resetFilters,
    generateResults,
    updateStatistics,
    generateCertificate,
    refreshResults,
    
    // Helper methods
    getSubjectName,
    getPaperName,
    getGradeBadgeClass,
  };
});
