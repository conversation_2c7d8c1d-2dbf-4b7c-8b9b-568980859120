<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ isEditing ? 'Edit Subject' : 'Add New Subject' }}
              </h3>

              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Subject Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject Name</label>
                  <input
                    v-model="formData.name"
                    type="text"
                    required
                    placeholder="Enter subject name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Subject Code -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject Code</label>
                  <input
                    v-model="formData.code"
                    type="text"
                    required
                    placeholder="Enter subject code (e.g., MATH001)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Description -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    v-model="formData.description"
                    rows="3"
                    placeholder="Enter subject description (optional)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  ></textarea>
                </div>

                <!-- Active Status -->
                <div class="flex items-center">
                  <input
                    v-model="formData.isActive"
                    type="checkbox"
                    id="isActive"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                  />
                  <label for="isActive" class="ml-2 block text-sm text-gray-700">
                    Subject is active
                  </label>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- Subject Preview -->
                <div v-if="formData.name && formData.code" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Subject Preview</h4>
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                      <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                      </svg>
                    </div>
                    <div>
                      <div class="font-medium text-gray-900">{{ formData.name }}</div>
                      <div class="text-sm text-gray-600">{{ formData.code }}</div>
                      <div v-if="formData.description" class="text-xs text-gray-500 mt-1">{{ formData.description }}</div>
                    </div>
                    <div class="ml-auto">
                      <span
                        :class="formData.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                        class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      >
                        {{ formData.isActive ? 'Active' : 'Inactive' }}
                      </span>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="isLoading || !isFormValid"
            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Saving...' : (isEditing ? 'Update Subject' : 'Add Subject') }}
          </button>
          <button
            @click="closeModal"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import type { SubjectDto, CreateSubjectRequest, UpdateSubjectRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  subject?: SubjectDto | null
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  subject: null
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const formData = ref<{
  name: string
  code: string
  description: string
  isActive: boolean
}>({
  name: '',
  code: '',
  description: '',
  isActive: true
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isEditing = computed(() => !!props.subject?.id)

const isFormValid = computed(() => {
  return formData.value.name.trim() && formData.value.code.trim()
})

// Methods
const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    description: '',
    isActive: true
  }
  error.value = null
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    if (isEditing.value && props.subject?.id) {
      // Update existing subject
      const updateData: UpdateSubjectRequest = {
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.updateSubject(props.subject.id, updateData)
      await sweetAlert.toast.success('Subject updated successfully')
    } else {
      // Create new subject
      const createData: CreateSubjectRequest = {
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.createSubject(createData)
      await sweetAlert.toast.success('Subject added successfully')
    }

    emit('success')
  } catch (err: any) {
    error.value = err.message || 'Failed to save subject'
  } finally {
    isLoading.value = false
  }
}

// Watch for subject prop changes
watch(() => props.subject, (newSubject) => {
  if (newSubject) {
    formData.value = {
      name: newSubject.name || '',
      code: newSubject.code || '',
      description: newSubject.description || '',
      isActive: newSubject.isActive ?? true
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
