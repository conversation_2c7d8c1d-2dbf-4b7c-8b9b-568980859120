<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
                {{ isEditing ? 'Edit Paper' : 'Add New Paper' }}
              </h3>

              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- Subject -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                  <select
                    v-model="formData.subjectId"
                    required
                    :disabled="!!preSelectedSubject"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm disabled:bg-gray-100"
                  >
                    <option value="">Select a subject</option>
                    <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                      {{ subject.name }} ({{ subject.code }})
                    </option>
                  </select>
                </div>

                <!-- Paper Name -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Paper Name</label>
                  <input
                    v-model="formData.name"
                    type="text"
                    required
                    placeholder="Enter paper name"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Paper Code -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Paper Code</label>
                  <input
                    v-model="formData.code"
                    type="text"
                    required
                    placeholder="Enter paper code (e.g., MATH-P1)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Paper Number -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Paper Number</label>
                  <input
                    v-model.number="formData.paperNumber"
                    type="number"
                    required
                    min="1"
                    max="10"
                    placeholder="Enter paper number"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  />
                </div>

                <!-- Duration and Max Marks -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                    <input
                      v-model.number="formData.duration"
                      type="number"
                      min="30"
                      max="300"
                      placeholder="Duration"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Marks</label>
                    <input
                      v-model.number="formData.maxMarks"
                      type="number"
                      min="1"
                      max="200"
                      placeholder="Max marks"
                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                    />
                  </div>
                </div>

                <!-- Description -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                  <textarea
                    v-model="formData.description"
                    rows="3"
                    placeholder="Enter paper description (optional)"
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
                  ></textarea>
                </div>

                <!-- Active Status -->
                <div class="flex items-center">
                  <input
                    v-model="formData.isActive"
                    type="checkbox"
                    id="isActive"
                    class="h-4 w-4 text-maneb-primary focus:ring-maneb-primary border-gray-300 rounded"
                  />
                  <label for="isActive" class="ml-2 block text-sm text-gray-700">
                    Paper is active
                  </label>
                </div>

                <!-- Error Message -->
                <div v-if="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p class="text-sm text-red-600">{{ error }}</p>
                </div>

                <!-- Paper Preview -->
                <div v-if="formData.name && formData.code" class="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">Paper Preview</h4>
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <div>
                        <div class="font-medium text-gray-900">{{ formData.name }}</div>
                        <div class="text-sm text-gray-600">{{ formData.code }} • Paper {{ formData.paperNumber }}</div>
                        <div v-if="formData.description" class="text-xs text-gray-500 mt-1">{{ formData.description }}</div>
                      </div>
                      <div class="text-right">
                        <span
                          :class="formData.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                          class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                        >
                          {{ formData.isActive ? 'Active' : 'Inactive' }}
                        </span>
                      </div>
                    </div>
                    <div v-if="formData.duration || formData.maxMarks" class="flex items-center space-x-4 text-sm text-gray-600">
                      <span v-if="formData.duration">Duration: {{ formatDuration(formData.duration) }}</span>
                      <span v-if="formData.maxMarks">Max Marks: {{ formData.maxMarks }}</span>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Modal Actions -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="handleSubmit"
            :disabled="isLoading || !isFormValid"
            class="w-full inline-flex justify-center rounded-lg border border-transparent shadow-sm px-4 py-2 bg-maneb-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isLoading ? 'Saving...' : (isEditing ? 'Update Paper' : 'Add Paper') }}
          </button>
          <button
            @click="closeModal"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-lg border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-maneb-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useGradingStore } from '@/store'
import type { PaperDto, CreatePaperRequest, UpdatePaperRequest } from '@/interfaces'
import sweetAlert from '@/utils/sweetAlert'

// Props
interface Props {
  isOpen: boolean
  paper?: PaperDto | null
  preSelectedSubject?: string
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
  paper: null,
  preSelectedSubject: ''
})

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Store
const gradingStore = useGradingStore()

// State
const formData = ref<{
  subjectId: string
  name: string
  code: string
  description: string
  paperNumber: number
  duration: number | null
  maxMarks: number | null
  isActive: boolean
}>({
  subjectId: '',
  name: '',
  code: '',
  description: '',
  paperNumber: 1,
  duration: null,
  maxMarks: null,
  isActive: true
})

const isLoading = ref(false)
const error = ref<string | null>(null)

// Computed
const isEditing = computed(() => !!props.paper?.id)

const isFormValid = computed(() => {
  return formData.value.subjectId &&
         formData.value.name.trim() &&
         formData.value.code.trim() &&
         formData.value.paperNumber > 0
})

// Methods
const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
  return `${mins}m`
}

const resetForm = () => {
  formData.value = {
    subjectId: props.preSelectedSubject || '',
    name: '',
    code: '',
    description: '',
    paperNumber: 1,
    duration: null,
    maxMarks: null,
    isActive: true
  }
  error.value = null
}

const closeModal = () => {
  resetForm()
  emit('close')
}

const handleSubmit = async () => {
  if (!isFormValid.value) return

  isLoading.value = true
  error.value = null

  try {
    if (isEditing.value && props.paper?.id) {
      // Update existing paper
      const updateData: UpdatePaperRequest = {
        subjectId: formData.value.subjectId,
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        paperNumber: formData.value.paperNumber,
        duration: formData.value.duration || undefined,
        maxMarks: formData.value.maxMarks || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.updatePaper(props.paper.id, updateData)
      await sweetAlert.toast.success('Paper updated successfully')
    } else {
      // Create new paper
      const createData: CreatePaperRequest = {
        subjectId: formData.value.subjectId,
        name: formData.value.name.trim(),
        code: formData.value.code.trim().toUpperCase(),
        description: formData.value.description.trim() || undefined,
        paperNumber: formData.value.paperNumber,
        duration: formData.value.duration || undefined,
        maxMarks: formData.value.maxMarks || undefined,
        isActive: formData.value.isActive
      }
      await gradingStore.createPaper(createData)
      await sweetAlert.toast.success('Paper added successfully')
    }

    emit('success')
  } catch (err: any) {
    error.value = err.message || 'Failed to save paper'
  } finally {
    isLoading.value = false
  }
}

// Watch for paper prop changes
watch(() => props.paper, (newPaper) => {
  if (newPaper) {
    formData.value = {
      subjectId: newPaper.subjectId || '',
      name: newPaper.name || '',
      code: newPaper.code || '',
      description: newPaper.description || '',
      paperNumber: newPaper.paperNumber || 1,
      duration: newPaper.duration || null,
      maxMarks: newPaper.maxMarks || null,
      isActive: newPaper.isActive ?? true
    }
  } else {
    resetForm()
  }
}, { immediate: true })

// Watch for modal open/close
watch(() => props.isOpen, (isOpen) => {
  if (!isOpen) {
    resetForm()
  } else if (props.preSelectedSubject) {
    formData.value.subjectId = props.preSelectedSubject
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
