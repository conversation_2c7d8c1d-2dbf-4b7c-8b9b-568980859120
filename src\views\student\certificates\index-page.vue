<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-7xl mx-auto">
      <!-- Header -->
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">My Certificates</h1>
            <p class="text-gray-600">View and download your examination certificates</p>
          </div>
          <router-link
            to="/student/dashboard"
            class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors duration-200"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Dashboard
          </router-link>
        </div>
      </div>

      <!-- Loading State -->
      <div v-if="isLoading" class="bg-white rounded-lg shadow-sm p-8 text-center">
        <div class="inline-flex items-center">
          <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Loading certificates...
        </div>
      </div>

      <!-- Available Certificates -->
      <div v-else-if="availableCertificates.length > 0" class="space-y-6">
        <div
          v-for="cert in availableCertificates"
          :key="`${cert.year}-${cert.studentId}`"
          class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden"
        >
          <div class="p-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <!-- Certificate Icon -->
                <div class="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center mr-4">
                  <svg class="h-6 w-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                  </svg>
                </div>

                <div>
                  <h3 class="text-lg font-semibold text-gray-900">{{ cert.year }} Examination Certificate</h3>
                  <p class="text-sm text-gray-600">{{ cert.examSession }}</p>
                  <div class="flex items-center mt-2 space-x-4 text-sm text-gray-500">
                    <span>{{ cert.subjects.length }} subjects</span>
                    <span>•</span>
                    <span>{{ cert.overallPerformance.passedSubjects }} passed</span>
                    <span>•</span>
                    <span class="font-medium" :class="getOverallGradeClass(cert.overallPerformance.overallGrade)">
                      Overall Grade: {{ cert.overallPerformance.overallGrade }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-3">
                <button
                  @click="viewCertificate(cert)"
                  class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  View Certificate
                </button>

                <button
                  @click="downloadCertificate(cert)"
                  class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Download
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Certificates State -->
      <div v-else class="bg-white rounded-lg shadow-sm p-8 text-center">
        <!-- MANEB Themed Certificate Icon -->
        <div class="mx-auto w-20 h-20 bg-red-50 rounded-full flex items-center justify-center mb-6">
          <svg class="h-10 w-10" fill="none" stroke="#a12c2c" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Certificates Available</h3>
        <p class="text-gray-500">You don't have any examination certificates available yet. Certificates will appear here once your results are processed.</p>
      </div>

      <!-- Certificate Modal -->
      <div v-if="selectedCertificate" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50" @click="closeCertificateModal">
        <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto" @click.stop>
          <div class="p-4 border-b border-gray-200 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-900">Certificate Preview</h3>
            <button
              @click="closeCertificateModal"
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
          <div class="p-4">
            <CertificateTemplate :certificate="selectedCertificate" />
          </div>
          <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
            <button
              @click="printCertificate(selectedCertificate)"
              class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 transition-colors duration-200"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
              </svg>
              Print Certificate
            </button>
            <button
              @click="closeCertificateModal"
              class="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-200 transition-colors duration-200"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useAuthStore } from '@/store/auth.store'
import { useResultsStore } from '@/store/results.store'
import { useGradingStore } from '@/store/grading.store'
import CertificateTemplate from '@/components/certificates/CertificateTemplate.vue'
import { pdfService } from '@/services/pdf.service'
import type { StudentCertificateDto, GradeLevel } from '@/interfaces'
import { sweetAlert } from '@/utils/sweetAlert'

// Stores
const authStore = useAuthStore()
const resultsStore = useResultsStore()
const gradingStore = useGradingStore()

// State
const isLoading = ref(true)
const availableCertificates = ref<StudentCertificateDto[]>([])
const selectedCertificate = ref<StudentCertificateDto | null>(null)

// Computed
const studentId = computed(() => authStore.user?.id || 'STU001') // Fallback for demo

// Methods
const loadAvailableCertificates = async () => {
  try {
    isLoading.value = true

    // Initialize grading store data first
    await gradingStore.initializeStore()

    // Generate results to get available years for this student
    await resultsStore.generateResults(
      gradingStore.gradeBoundaries,
      gradingStore.subjects,
      gradingStore.papers
    )

    // Find all years this student has results for
    const studentResults = resultsStore.results.filter(result =>
      result.studentId === studentId.value
    )

    const availableYears = [...new Set(studentResults.map(r => r.year))].sort((a, b) => b - a)

    // Generate certificates for each available year
    const certificates: StudentCertificateDto[] = []
    for (const year of availableYears) {
      try {
        const certificate = await resultsStore.generateCertificate(
          studentId.value,
          year,
          gradingStore.gradeBoundaries,
          gradingStore.subjects,
          gradingStore.papers
        )
        certificates.push(certificate)
      } catch (error) {
        console.error(`Failed to generate certificate for year ${year}:`, error)
      }
    }

    availableCertificates.value = certificates
  } catch (error) {
    console.error('Failed to load certificates:', error)
    await sweetAlert.error('Error', 'Failed to load certificates')
  } finally {
    isLoading.value = false
  }
}

const viewCertificate = (certificate: StudentCertificateDto) => {
  selectedCertificate.value = certificate
}

const closeCertificateModal = () => {
  selectedCertificate.value = null
}

const downloadCertificate = async (certificate: StudentCertificateDto) => {
  try {
    // Show loading message
    await sweetAlert.info(
      'Generating PDF',
      'Generating PDF certificate for download...',
      'info'
    )

    // Generate and download PDF
    await pdfService.generateCertificatePDF(certificate)

    await sweetAlert.success(
      'Download Complete',
      'Certificate PDF has been downloaded successfully!'
    )
  } catch (error: any) {
    await sweetAlert.error('Error', error.message || 'Failed to download certificate')
  }
}

const printCertificate = async (certificate: StudentCertificateDto) => {
  try {
    // Open certificate in new window for printing
    const certificateWindow = window.open('', '_blank', 'width=800,height=600')
    if (certificateWindow) {
      certificateWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>MANEB Certificate - ${certificate.studentId}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
            .certificate { max-width: 800px; margin: 0 auto; }
            .header { text-align: center; border-bottom: 3px solid #a12c2c; padding: 20px; }
            .content { padding: 20px; }
            .subject { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
            .grade-a { background: #dcfce7; color: #166534; }
            .grade-b { background: #dbeafe; color: #1e40af; }
            .grade-c { background: #fef3c7; color: #92400e; }
            .grade-d { background: #fed7aa; color: #c2410c; }
            .grade-f { background: #fecaca; color: #dc2626; }
            @media print { body { margin: 0; } }
          </style>
        </head>
        <body>
          <div class="certificate">
            <div class="header">
              <h1>MALAWI NATIONAL EXAMINATIONS BOARD</h1>
              <h2>CERTIFICATE OF EXAMINATION</h2>
              <p>${certificate.examSession}</p>
              <p>Certificate No: ${certificate.certificateNumber}</p>
            </div>
            <div class="content">
              <p><strong>Student:</strong> ${certificate.studentId}</p>
              <p><strong>Year:</strong> ${certificate.year}</p>
              <h3>Subjects and Results:</h3>
              ${certificate.subjects.map(subject => `
                <div class="subject">
                  <h4>${subject.subjectName} (${subject.subjectCode})</h4>
                  <p><strong>Overall Grade:</strong> <span class="grade-${subject.overallGrade.toLowerCase()}">${subject.overallGrade}</span></p>
                  <p><strong>Status:</strong> ${subject.overallPassStatus ? 'PASS' : 'FAIL'}</p>
                  <div>
                    ${subject.papers.map(paper => `
                      <p>${paper.paperName}: ${paper.score}% (Grade ${paper.grade})</p>
                    `).join('')}
                  </div>
                </div>
              `).join('')}
              <div style="margin-top: 30px; text-align: center;">
                <h3>Overall Performance</h3>
                <p>Total Subjects: ${certificate.overallPerformance.totalSubjects}</p>
                <p>Passed: ${certificate.overallPerformance.passedSubjects}</p>
                <p>Overall Grade: ${certificate.overallPerformance.overallGrade}</p>
                <p>Pass Rate: ${certificate.overallPerformance.passRate.toFixed(1)}%</p>
              </div>
              <div style="margin-top: 30px; text-align: center; border-top: 1px solid #ddd; padding-top: 20px;">
                <p><strong>Issued by:</strong> ${certificate.issuedBy}</p>
                <p><strong>Issue Date:</strong> ${new Date(certificate.issueDate).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
          <script>
            window.onload = function() {
              window.print();
              window.onafterprint = function() {
                window.close();
              }
            }
          </script>
        </body>
        </html>
      `)
      certificateWindow.document.close()
    } else {
      await sweetAlert.error('Error', 'Unable to open certificate window. Please check your popup blocker settings.')
    }
  } catch (error) {
    await sweetAlert.error('Error', 'Failed to print certificate')
  }
}

const getOverallGradeClass = (grade: GradeLevel): string => {
  const classes = {
    'A': 'text-green-600',
    'B': 'text-blue-600',
    'C': 'text-yellow-600',
    'D': 'text-orange-600',
    'F': 'text-red-600'
  }
  return classes[grade] || 'text-gray-600'
}

// Lifecycle
onMounted(() => {
  loadAvailableCertificates()
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.hover-text-maneb-primary-dark:hover {
  color: #8b2424;
}
</style>
