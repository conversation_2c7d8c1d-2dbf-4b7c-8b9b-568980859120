<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Paper Management</h2>
          <p class="text-sm text-gray-600 mt-1">Manage examination papers for each subject</p>
        </div>
        <button
          @click="showPaperModal = true"
          class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Paper
        </button>
      </div>

      <!-- Filters -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Search Papers</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search by name or code..."
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          />
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Filter by Subject</label>
          <select
            v-model="selectedSubject"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
          >
            <option value="">All Subjects</option>
            <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
              {{ subject.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Papers by Subject -->
    <div v-if="gradingStore.isLoading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading papers...
      </div>
    </div>

    <div v-else-if="groupedPapers.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No papers found</p>
      <p class="text-gray-600">Get started by adding your first examination paper.</p>
    </div>

    <div v-else class="space-y-6">
      <div v-for="group in groupedPapers" :key="group.subjectId" class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ getSubjectName(group.subjectId) }}</h3>
              <p class="text-sm text-gray-600">{{ getSubjectCode(group.subjectId) }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-sm text-gray-500">{{ group.papers.length }} papers</span>
              <button
                @click="addPaperToSubject(group.subjectId)"
                class="text-maneb-primary hover:text-red-700 text-sm font-medium transition-colors duration-200"
              >
                Add Paper
              </button>
            </div>
          </div>
        </div>

        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="paper in group.papers"
              :key="paper.id"
              class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
            >
              <!-- Paper Header -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                      <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                      </svg>
                    </div>
                  </div>
                  <div>
                    <h4 class="font-semibold text-gray-900">{{ paper.name }}</h4>
                    <p class="text-sm text-gray-600">{{ paper.code }}</p>
                  </div>
                </div>
                <span
                  :class="paper.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                >
                  {{ paper.isActive ? 'Active' : 'Inactive' }}
                </span>
              </div>

              <!-- Paper Details -->
              <div class="space-y-2 mb-4">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">Paper Number:</span>
                  <span class="font-medium text-gray-900">{{ paper.paperNumber }}</span>
                </div>
                <div v-if="paper.duration" class="flex justify-between text-sm">
                  <span class="text-gray-600">Duration:</span>
                  <span class="font-medium text-gray-900">{{ formatDuration(paper.duration) }}</span>
                </div>
                <div v-if="paper.maxMarks" class="flex justify-between text-sm">
                  <span class="text-gray-600">Max Marks:</span>
                  <span class="font-medium text-gray-900">{{ paper.maxMarks }}</span>
                </div>
              </div>

              <!-- Paper Description -->
              <div v-if="paper.description" class="mb-4">
                <p class="text-sm text-gray-600">{{ paper.description }}</p>
              </div>

              <!-- Paper Stats -->
              <div class="grid grid-cols-2 gap-2 mb-4">
                <div class="text-center p-2 bg-gray-50 rounded">
                  <div class="text-sm font-semibold text-gray-900">{{ getGradeCount(paper.id!) }}</div>
                  <div class="text-xs text-gray-600">Grades</div>
                </div>
                <div class="text-center p-2 bg-gray-50 rounded">
                  <div class="text-sm font-semibold text-gray-900">{{ getBoundaryCount(paper.id!) }}</div>
                  <div class="text-xs text-gray-600">Boundaries</div>
                </div>
              </div>

              <!-- Actions -->
              <div class="flex items-center justify-between pt-3 border-t border-gray-200">
                <button
                  @click="viewPaperDetails(paper)"
                  class="text-sm text-maneb-primary hover:text-red-700 font-medium transition-colors duration-200"
                >
                  View Details
                </button>
                <div class="flex items-center space-x-2">
                  <button
                    @click="editPaper(paper)"
                    class="p-2 text-gray-400 hover:text-maneb-primary transition-colors duration-200"
                    title="Edit Paper"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                  </button>
                  <button
                    @click="deletePaper(paper)"
                    class="p-2 text-gray-400 hover:text-red-600 transition-colors duration-200"
                    title="Delete Paper"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Paper Modal -->
    <PaperModal
      :is-open="showPaperModal"
      :paper="selectedPaper"
      :pre-selected-subject="preSelectedSubject"
      @close="closePaperModal"
      @success="handlePaperSuccess"
    />

    <!-- Paper Details Modal -->
    <PaperDetailsModal
      :is-open="showDetailsModal"
      :paper="selectedPaper"
      @close="closeDetailsModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradingStore } from '@/store'
import type { PaperDto } from '@/interfaces'
import PaperModal from './PaperModal.vue'
import PaperDetailsModal from './PaperDetailsModal.vue'
import sweetAlert from '@/utils/sweetAlert'

// Store
const gradingStore = useGradingStore()

// State
const showPaperModal = ref(false)
const showDetailsModal = ref(false)
const selectedPaper = ref<PaperDto | null>(null)
const searchQuery = ref('')
const selectedSubject = ref('')
const preSelectedSubject = ref('')

// Computed
const filteredPapers = computed(() => {
  let filtered = gradingStore.papers

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(paper => 
      paper.name?.toLowerCase().includes(query) ||
      paper.code?.toLowerCase().includes(query)
    )
  }

  if (selectedSubject.value) {
    filtered = filtered.filter(paper => paper.subjectId === selectedSubject.value)
  }

  return filtered.filter(paper => !paper.isDeleted)
})

const groupedPapers = computed(() => {
  const groups: { [key: string]: { subjectId: string; papers: PaperDto[] } } = {}

  filteredPapers.value.forEach(paper => {
    if (!groups[paper.subjectId]) {
      groups[paper.subjectId] = {
        subjectId: paper.subjectId,
        papers: []
      }
    }
    groups[paper.subjectId].papers.push(paper)
  })

  // Sort papers within each group by paper number
  Object.values(groups).forEach(group => {
    group.papers.sort((a, b) => a.paperNumber - b.paperNumber)
  })

  return Object.values(groups).sort((a, b) => {
    return getSubjectName(a.subjectId).localeCompare(getSubjectName(b.subjectId))
  })
})

// Methods
const getSubjectName = (subjectId: string): string => {
  const subject = gradingStore.subjects.find(s => s.id === subjectId)
  return subject?.name || 'Unknown Subject'
}

const getSubjectCode = (subjectId: string): string => {
  const subject = gradingStore.subjects.find(s => s.id === subjectId)
  return subject?.code || 'N/A'
}

const getGradeCount = (paperId: string): number => {
  return gradingStore.grades.filter(grade => grade.paperId === paperId).length
}

const getBoundaryCount = (paperId: string): number => {
  return gradingStore.gradeBoundaries.filter(boundary => boundary.paperId === paperId).length
}

const formatDuration = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
  return `${mins}m`
}

const addPaperToSubject = (subjectId: string) => {
  preSelectedSubject.value = subjectId
  selectedPaper.value = null
  showPaperModal.value = true
}

const editPaper = (paper: PaperDto) => {
  selectedPaper.value = paper
  preSelectedSubject.value = ''
  showPaperModal.value = true
}

const viewPaperDetails = (paper: PaperDto) => {
  selectedPaper.value = paper
  showDetailsModal.value = true
}

const deletePaper = async (paper: PaperDto) => {
  const gradeCount = getGradeCount(paper.id!)
  const boundaryCount = getBoundaryCount(paper.id!)
  
  let message = `Are you sure you want to delete "${paper.name}"?`
  if (gradeCount > 0 || boundaryCount > 0) {
    message += `\n\nThis will also delete:\n• ${gradeCount} grade record(s)\n• ${boundaryCount} grade boundary(ies)`
  }
  message += '\n\nThis action cannot be undone.'

  const result = await sweetAlert.confirm(
    'Delete Paper',
    message,
    'warning'
  )

  if (result.isConfirmed && paper.id) {
    try {
      await gradingStore.deletePaper(paper.id)
      await sweetAlert.toast.success('Paper deleted successfully')
    } catch (error) {
      await sweetAlert.error('Error', 'Failed to delete paper')
    }
  }
}

const closePaperModal = () => {
  showPaperModal.value = false
  selectedPaper.value = null
  preSelectedSubject.value = ''
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedPaper.value = null
}

const handlePaperSuccess = () => {
  closePaperModal()
  // Refresh papers list
  gradingStore.fetchPapers()
}

// Lifecycle
onMounted(() => {
  if (gradingStore.papers.length === 0) {
    gradingStore.fetchPapers()
  }
  if (gradingStore.subjects.length === 0) {
    gradingStore.fetchSubjects()
  }
  if (gradingStore.grades.length === 0) {
    gradingStore.fetchGrades()
  }
  if (gradingStore.gradeBoundaries.length === 0) {
    gradingStore.fetchGradeBoundaries()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
