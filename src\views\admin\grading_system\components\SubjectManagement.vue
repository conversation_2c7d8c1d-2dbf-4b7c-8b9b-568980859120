<template>
  <div class="space-y-6">
    <!-- Header with Actions -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div class="flex items-center justify-between mb-6">
        <div>
          <h2 class="text-xl font-semibold text-gray-900">Subject Management</h2>
          <p class="text-sm text-gray-600 mt-1">Manage subjects and their configurations</p>
        </div>
        <button
          @click="showSubjectModal = true"
          class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200"
        >
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Subject
        </button>
      </div>

      <!-- Search -->
      <div class="max-w-md">
        <label class="block text-sm font-medium text-gray-700 mb-2">Search Subjects</label>
        <input
          v-model="gradingStore.searchQuery"
          type="text"
          placeholder="Search by name or code..."
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
        />
      </div>
    </div>

    <!-- Subjects Grid -->
    <div v-if="gradingStore.isLoading" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
      <div class="inline-flex items-center">
        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-maneb-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Loading subjects...
      </div>
    </div>

    <div v-else-if="gradingStore.filteredSubjects.length === 0" class="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center text-gray-500">
      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
      </svg>
      <p class="text-lg font-medium text-gray-900 mb-2">No subjects found</p>
      <p class="text-gray-600">Get started by adding your first subject.</p>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div
        v-for="subject in gradingStore.filteredSubjects"
        :key="subject.id"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
      >
        <!-- Subject Header -->
        <div class="flex items-start justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-maneb-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                </svg>
              </div>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-900">{{ subject.name }}</h3>
              <p class="text-sm text-gray-600">{{ subject.code }}</p>
            </div>
          </div>
          <div class="flex items-center space-x-2">
            <span
              :class="subject.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
              class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
            >
              {{ subject.isActive ? 'Active' : 'Inactive' }}
            </span>
          </div>
        </div>

        <!-- Subject Description -->
        <div v-if="subject.description" class="mb-4">
          <p class="text-sm text-gray-600">{{ subject.description }}</p>
        </div>

        <!-- Subject Stats -->
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center p-3 bg-gray-50 rounded-lg">
            <div class="text-lg font-semibold text-gray-900">{{ getGradeCount(subject.id!) }}</div>
            <div class="text-xs text-gray-600">Total Grades</div>
          </div>
          <div class="text-center p-3 bg-gray-50 rounded-lg">
            <div class="text-lg font-semibold text-gray-900">{{ getBoundaryCount(subject.id!) }}</div>
            <div class="text-xs text-gray-600">Grade Boundaries</div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-200">
          <button
            @click="viewSubjectDetails(subject)"
            class="text-sm text-maneb-primary hover:text-red-700 font-medium transition-colors duration-200"
          >
            View Details
          </button>
          <div class="flex items-center space-x-2">
            <button
              @click="editSubject(subject)"
              class="p-2 text-gray-400 hover:text-maneb-primary transition-colors duration-200"
              title="Edit Subject"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
            </button>
            <button
              @click="deleteSubject(subject)"
              class="p-2 text-gray-400 hover:text-red-600 transition-colors duration-200"
              title="Delete Subject"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Subject Modal -->
    <SubjectModal
      :is-open="showSubjectModal"
      :subject="selectedSubject"
      @close="closeSubjectModal"
      @success="handleSubjectSuccess"
    />

    <!-- Subject Details Modal -->
    <SubjectDetailsModal
      :is-open="showDetailsModal"
      :subject="selectedSubject"
      @close="closeDetailsModal"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useGradingStore } from '@/store'
import type { SubjectDto } from '@/interfaces'
import SubjectModal from './SubjectModal.vue'
import SubjectDetailsModal from './SubjectDetailsModal.vue'
import sweetAlert from '@/utils/sweetAlert'

// Store
const gradingStore = useGradingStore()

// State
const showSubjectModal = ref(false)
const showDetailsModal = ref(false)
const selectedSubject = ref<SubjectDto | null>(null)

// Methods
const getGradeCount = (subjectId: string): number => {
  return gradingStore.grades.filter(grade => grade.subjectId === subjectId).length
}

const getBoundaryCount = (subjectId: string): number => {
  return gradingStore.gradeBoundaries.filter(boundary => boundary.subjectId === subjectId).length
}

const editSubject = (subject: SubjectDto) => {
  selectedSubject.value = subject
  showSubjectModal.value = true
}

const viewSubjectDetails = (subject: SubjectDto) => {
  selectedSubject.value = subject
  showDetailsModal.value = true
}

const deleteSubject = async (subject: SubjectDto) => {
  const gradeCount = getGradeCount(subject.id!)
  const boundaryCount = getBoundaryCount(subject.id!)
  
  let message = `Are you sure you want to delete "${subject.name}"?`
  if (gradeCount > 0 || boundaryCount > 0) {
    message += `\n\nThis will also delete:\n• ${gradeCount} grade record(s)\n• ${boundaryCount} grade boundary(ies)`
  }
  message += '\n\nThis action cannot be undone.'

  const result = await sweetAlert.confirm(
    'Delete Subject',
    message,
    'warning'
  )

  if (result.isConfirmed && subject.id) {
    try {
      await gradingStore.deleteSubject(subject.id)
      await sweetAlert.toast.success('Subject deleted successfully')
    } catch (error) {
      await sweetAlert.error('Error', 'Failed to delete subject')
    }
  }
}

const closeSubjectModal = () => {
  showSubjectModal.value = false
  selectedSubject.value = null
}

const closeDetailsModal = () => {
  showDetailsModal.value = false
  selectedSubject.value = null
}

const handleSubjectSuccess = () => {
  closeSubjectModal()
  // Refresh subjects list
  gradingStore.fetchSubjects()
}

// Lifecycle
onMounted(() => {
  if (gradingStore.subjects.length === 0) {
    gradingStore.fetchSubjects()
  }
  if (gradingStore.grades.length === 0) {
    gradingStore.fetchGrades()
  }
  if (gradingStore.gradeBoundaries.length === 0) {
    gradingStore.fetchGradeBoundaries()
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.hover\:text-maneb-primary:hover {
  color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
