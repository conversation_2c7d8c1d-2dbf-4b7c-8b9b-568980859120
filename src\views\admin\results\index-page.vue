<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b border-gray-200">
      <div class="px-6 py-4">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-semibold text-gray-900">Results Management</h1>
            <p class="text-sm text-gray-600 mt-1">View automatically calculated student grades and generate certificates</p>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="refreshResults"
              :disabled="resultsStore.isLoading"
              class="inline-flex items-center px-4 py-2 bg-maneb-primary text-white text-sm font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2 transition-colors duration-200 disabled:opacity-50"
            >
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
              Refresh Results
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Content -->
    <div class="p-6">
      <!-- Statistics Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Total Results</p>
              <p class="text-2xl font-semibold text-gray-900">{{ resultsStore.statistics.totalResults }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-green-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Passed</p>
              <p class="text-2xl font-semibold text-gray-900">{{ resultsStore.statistics.passCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-red-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Failed</p>
              <p class="text-2xl font-semibold text-gray-900">{{ resultsStore.statistics.failCount }}</p>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="w-10 h-10 bg-yellow-50 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Pass Rate</p>
              <p class="text-2xl font-semibold text-gray-900">{{ Math.round(resultsStore.statistics.passRate) }}%</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Results</h3>
        <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              v-model="resultsStore.filters.searchQuery"
              type="text"
              placeholder="Search by student ID..."
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Year</label>
            <select
              v-model="resultsStore.filters.year"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Years</option>
              <option v-for="year in resultsStore.availableYears" :key="year" :value="year">{{ year }}</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
            <select
              v-model="resultsStore.filters.subjectId"
              @change="onSubjectChange"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Subjects</option>
              <option v-for="subject in gradingStore.activeSubjects" :key="subject.id" :value="subject.id">
                {{ subject.name }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Paper</label>
            <select
              v-model="resultsStore.filters.paperId"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Papers</option>
              <option v-for="paperId in resultsStore.availablePapers" :key="paperId" :value="paperId">
                {{ resultsStore.getPaperName(paperId, gradingStore.papers) }}
              </option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Grade</label>
            <select
              v-model="resultsStore.filters.gradeLevel"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Grades</option>
              <option value="A">Grade A</option>
              <option value="B">Grade B</option>
              <option value="C">Grade C</option>
              <option value="D">Grade D</option>
              <option value="F">Grade F</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
            <select
              v-model="resultsStore.filters.passStatus"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-maneb-primary focus:border-maneb-primary text-sm"
            >
              <option value="All">All Status</option>
              <option :value="true">Pass</option>
              <option :value="false">Fail</option>
            </select>
          </div>
        </div>
        <div class="mt-4 flex items-center space-x-3">
          <button
            @click="resultsStore.resetFilters()"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-maneb-primary focus:ring-offset-2"
          >
            Reset Filters
          </button>
        </div>
      </div>

      <!-- Results Table -->
      <ResultsTable />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useResultsStore } from '@/store/results.store'
import { useGradingStore } from '@/store/grading.store'
import ResultsTable from './components/ResultsTable.vue'
import sweetAlert from '@/utils/sweetAlert'

// Stores
const resultsStore = useResultsStore()
const gradingStore = useGradingStore()

// Methods
const refreshResults = async () => {
  try {
    await resultsStore.generateResults(
      gradingStore.gradeBoundaries,
      gradingStore.subjects,
      gradingStore.papers
    )
    await sweetAlert.toast.success('Results refreshed successfully')
  } catch (error) {
    await sweetAlert.error('Error', 'Failed to refresh results')
  }
}

const onSubjectChange = () => {
  // Reset paper filter when subject changes
  resultsStore.filters.paperId = 'All'
}

// Lifecycle
onMounted(async () => {
  try {
    // Initialize grading store data first
    await gradingStore.initializeStore()
    
    // Then generate results
    await resultsStore.generateResults(
      gradingStore.gradeBoundaries,
      gradingStore.subjects,
      gradingStore.papers
    )
  } catch (error) {
    console.error('Failed to load results data:', error)
  }
})
</script>

<style scoped>
/* MANEB Theme Colors */
.bg-maneb-primary {
  background-color: #a12c2c;
}

.text-maneb-primary {
  color: #a12c2c;
}

.border-maneb-primary {
  border-color: #a12c2c;
}

.hover\:bg-maneb-primary:hover {
  background-color: #a12c2c;
}

.focus\:ring-maneb-primary:focus {
  --tw-ring-color: #a12c2c;
}

.focus\:border-maneb-primary:focus {
  border-color: #a12c2c;
}
</style>
